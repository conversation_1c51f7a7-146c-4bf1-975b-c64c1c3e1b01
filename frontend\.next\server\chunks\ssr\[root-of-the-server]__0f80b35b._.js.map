{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Section.jsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\nimport Container from \"./Container\";\n\nexport default function Section({ \n  children, \n  className,\n  containerClassName,\n  containerSize = \"default\",\n  spacing = \"default\",\n  background = \"transparent\",\n  as: Component = \"section\",\n  ...props \n}) {\n  const spacingVariants = {\n    none: \"\",\n    sm: \"py-8 md:py-12\",\n    default: \"py-12 md:py-16 lg:py-20\",\n    lg: \"py-16 md:py-20 lg:py-24\",\n    xl: \"py-20 md:py-24 lg:py-32\"\n  };\n\n  const backgroundVariants = {\n    transparent: \"\",\n    white: \"bg-white\",\n    gray: \"bg-gray-50\",\n    primary: \"bg-primary/5\",\n    muted: \"bg-muted\"\n  };\n\n  return (\n    <Component\n      className={cn(\n        // Базовые стили секции\n        \"relative w-full\",\n        \n        // Отступы\n        spacingVariants[spacing],\n        \n        // Фон\n        backgroundVariants[background],\n        \n        className\n      )}\n      {...props}\n    >\n      <Container \n        size={containerSize}\n        className={containerClassName}\n      >\n        {children}\n      </Container>\n    </Component>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS,QAAQ,EAC9B,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,gBAAgB,SAAS,EACzB,UAAU,SAAS,EACnB,aAAa,aAAa,EAC1B,IAAI,YAAY,SAAS,EACzB,GAAG,OACJ;IACC,MAAM,kBAAkB;QACtB,MAAM;QACN,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uBAAuB;QACvB,mBAEA,UAAU;QACV,eAAe,CAAC,QAAQ,EAExB,MAAM;QACN,kBAAkB,CAAC,WAAW,EAE9B;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,yIAAA,CAAA,UAAS;YACR,MAAM;YACN,WAAW;sBAEV;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/index.js"], "sourcesContent": ["export { default as Container } from './Container';\nexport { default as Section } from './Section';\nexport { default as Header } from './Header';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/statusConfig.js"], "sourcesContent": ["import { Triangle<PERSON>lert, CheckCircle } from \"lucide-react\";\r\n\r\nexport default function getStatusConfig(status) {\r\n    const configs = {\r\n        new: {\r\n            icon: <PERSON><PERSON>lert,\r\n            label: \"Новый\",\r\n            className: \"bg-yellow-100 text-yellow-800 border-yellow-200\"\r\n        },\r\n        confirmed: {\r\n            icon: CheckCircle,\r\n            label: \"Подтвержден\",\r\n            className: \"bg-blue-100 text-blue-800 border-blue-200\"\r\n        },\r\n        completed: {\r\n            icon: CheckCircle,\r\n            label: \"Выполнен\",\r\n            className: \"bg-green-100 text-green-800 border-green-200\"\r\n        },\r\n        cancelled: {\r\n            icon: TriangleAlert,\r\n            label: \"Отменен\",\r\n            className: \"bg-red-100 text-red-800 border-red-200\"\r\n        }\r\n    };\r\n    return configs[status] || configs.new;\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEe,SAAS,gBAAgB,MAAM;IAC1C,MAAM,UAAU;QACZ,KAAK;YACD,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,WAAW;QACf;QACA,WAAW;YACP,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,WAAW;QACf;QACA,WAAW;YACP,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,WAAW;QACf;QACA,WAAW;YACP,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,WAAW;QACf;IACJ;IACA,OAAO,OAAO,CAAC,OAAO,IAAI,QAAQ,GAAG;AACzC", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/shared/OrderReviewCard.jsx"], "sourcesContent": ["import Link from \"next/link\"\r\nimport getStatusConfig from \"@/utils/statusConfig\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport default function OrderReviewCard({ order }) {\r\n    const statusConfig = getStatusConfig(order.status);\r\n    const StatusIcon = statusConfig.icon;\r\n\r\n    return (\r\n        <Link href={`/orders/${order.id}`}>\r\n            <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300 mb-8\">\r\n                <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                        <h3 className=\"text-lg font-semibold text-gray-900\">\r\n                            Заказ #{order.id}\r\n                        </h3>\r\n                        <div className={cn(\r\n                            \"inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium border\",\r\n                            statusConfig.className\r\n                        )}>\r\n                            <StatusIcon className=\"w-4 h-4\" />\r\n                            {statusConfig.label}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </Link>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS,gBAAgB,EAAE,KAAK,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,UAAe,AAAD,EAAE,MAAM,MAAM;IACjD,MAAM,aAAa,aAAa,IAAI;IAEpC,qBACI,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;kBAC7B,cAAA,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAG,WAAU;;gCAAsC;gCACxC,MAAM,EAAE;;;;;;;sCAEpB,8OAAC;4BAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,sFACA,aAAa,SAAS;;8CAEtB,8OAAC;oCAAW,WAAU;;;;;;gCACrB,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/app/page.js"], "sourcesContent": ["import { Section } from \"@/components/layout\";\r\nimport OrderReviewCard from \"@/components/shared/OrderReviewCard\";\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"@components/ui/accordion\";\r\n\r\nexport default function Home() {\r\n  // Преобразованные данные заказа для компонента OrderItem\r\n  const orderData = {\r\n    id: \"**********\",\r\n    user: {\r\n      first_name: \"Дан<PERSON>\",\r\n      phone: \"+7 (950) 079-32-65\"\r\n    },\r\n    delivery_type: \"Самовывоз\",\r\n    comment: \"Тестовый заказ, не пробовать\",\r\n    address: \"\", // Пустой для самовывоза\r\n    paymentsystem: \"banktransfer\",\r\n    status: \"new\", // Добавляем статус по умолчанию\r\n    amount: \"3150\",\r\n    meals: [\r\n      {\r\n        id: \"ehnOzTB06KH0dpL2HiZP\",\r\n        name: \"Окрошка на Квасе\",\r\n        quantity: 1400,\r\n        amount: 1400,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      },\r\n      {\r\n        id: \"meal2\",\r\n        name: \"Борщ украинский\",\r\n        quantity: 700,\r\n        amount: 700,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      },\r\n      {\r\n        id: \"meal3\",\r\n        name: \"Плов узбекский\",\r\n        quantity: 1050,\r\n        amount: 1050,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/55155502.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      }\r\n    ]\r\n  };\r\n\r\n  return (\r\n    <main>\r\n      <Section>\r\n        <OrderReviewCard order={orderData} />\r\n      </Section>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;;;;;;;;AAQe,SAAS;IACtB,yDAAyD;IACzD,MAAM,YAAY;QAChB,IAAI;QACJ,MAAM;YACJ,YAAY;YACZ,OAAO;QACT;QACA,eAAe;QACf,SAAS;QACT,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;SACD;IACH;IAEA,qBACE,8OAAC;kBACC,cAAA,8OAAC,6KAAA,CAAA,UAAO;sBACN,cAAA,8OAAC,+IAAA,CAAA,UAAe;gBAAC,OAAO;;;;;;;;;;;;;;;;AAIhC", "debugId": null}}]}