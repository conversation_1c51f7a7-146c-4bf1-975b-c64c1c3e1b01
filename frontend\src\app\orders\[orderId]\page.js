import { Section } from "@/components/layout";
import OrderCard from "@/components/shared/OrderCard";

export default function Order() {
  // Преобразованные данные заказа для компонента OrderItem
  const orderData = {
    id: "**********",
    user: {
      first_name: "<PERSON><PERSON><PERSON><PERSON>",
      phone: "+7 (950) 079-32-65"
    },
    delivery_type: "Самовывоз",
    comment: "Тестовый заказ, не пробовать",
    address: "", // Пустой для самовывоза
    paymentsystem: "banktransfer",
    status: "new", // Добавляем статус по умолчанию
    amount: "3150",
    meals: [
      {
        id: "ehnOzTB06KH0dpL2HiZP",
        name: "Окрошка на Квасе",
        quantity: 1400,
        amount: 1400,
        img: "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg",
        pack_m: "350",
        price: "1",
        unit: "г",
        portion: "350"
      },
      {
        id: "meal2",
        name: "Борщ украинский",
        quantity: 700,
        amount: 700,
        img: "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg",
        pack_m: "350",
        price: "1",
        unit: "г",
        portion: "350"
      },
      {
        id: "meal3",
        name: "Плов узбекский",
        quantity: 1050,
        amount: 1050,
        img: "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg",
        pack_m: "350",
        price: "1",
        unit: "г",
        portion: "350"
      }
    ]
  };

  return (
    <main>
      <Section>
        <OrderCard order={orderData} />
      </Section>
    </main>
  );
}
