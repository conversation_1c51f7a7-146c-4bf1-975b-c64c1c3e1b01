import { Triangle<PERSON>lert, CheckCircle } from "lucide-react";

export default function getStatusConfig(status) {
    const configs = {
        new: {
            icon: <PERSON><PERSON>lert,
            label: "Новый",
            className: "bg-yellow-100 text-yellow-800 border-yellow-200"
        },
        confirmed: {
            icon: CheckCircle,
            label: "Подтвержден",
            className: "bg-blue-100 text-blue-800 border-blue-200"
        },
        completed: {
            icon: CheckCircle,
            label: "Выполнен",
            className: "bg-green-100 text-green-800 border-green-200"
        },
        cancelled: {
            icon: TriangleAlert,
            label: "Отменен",
            className: "bg-red-100 text-red-800 border-red-200"
        }
    };
    return configs[status] || configs.new;
}