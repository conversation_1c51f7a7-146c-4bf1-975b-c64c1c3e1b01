import { Section } from "@/components/layout";
import OrderReviewCard from "@/components/shared/OrderReviewCard";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";

export default function Home() {
  // Преобразованные данные заказа для компонента OrderItem
  const orderData = {
    id: "**********",
    user: {
      first_name: "Дан<PERSON>",
      phone: "+7 (950) 079-32-65"
    },
    delivery_type: "Самовывоз",
    comment: "Тестовый заказ, не пробовать",
    address: "", // Пустой для самовывоза
    paymentsystem: "banktransfer",
    status: "new", // Добавляем статус по умолчанию
    amount: "3150",
    meals: [
      {
        id: "ehnOzTB06KH0dpL2HiZP",
        name: "Окрошка на Квасе",
        quantity: 1400,
        amount: 1400,
        img: "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg",
        pack_m: "350",
        price: "1",
        unit: "г",
        portion: "350"
      },
      {
        id: "meal2",
        name: "Борщ украинский",
        quantity: 700,
        amount: 700,
        img: "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg",
        pack_m: "350",
        price: "1",
        unit: "г",
        portion: "350"
      },
      {
        id: "meal3",
        name: "Плов узбекский",
        quantity: 1050,
        amount: 1050,
        img: "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg",
        pack_m: "350",
        price: "1",
        unit: "г",
        portion: "350"
      }
    ]
  };

  return (
    <main>
      <Section>
        <Accordion
          defaultValue="item-1"
          collapsible
          type="single"
        >
          <AccordionItem value="item-1">
            <AccordionTrigger className="text-lg font-semibold px-4">Новые заказы: 0</AccordionTrigger>
            <AccordionContent>
              <OrderReviewCard order={orderData} />
              <OrderReviewCard order={orderData} />
              <OrderReviewCard order={orderData} />
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2">
            <AccordionTrigger className="text-lg font-semibold px-4">Подтвержден: 10</AccordionTrigger>
            <AccordionContent>
              <OrderReviewCard order={orderData} />
              <OrderReviewCard order={orderData} />
              <OrderReviewCard order={orderData} />
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3">
            <AccordionTrigger className="text-lg font-semibold px-4">Готов: 2</AccordionTrigger>
            <AccordionContent>
              <OrderReviewCard order={orderData} />
              <OrderReviewCard order={orderData} />
              <OrderReviewCard order={orderData} />
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-4">
            <AccordionTrigger className="text-lg font-semibold px-4">
              В пути
              <Badge/>
            </AccordionTrigger>
            <AccordionContent>
              <OrderReviewCard order={orderData} />
              <OrderReviewCard order={orderData} />
              <OrderReviewCard order={orderData} />
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </Section>
    </main>
  );
}
